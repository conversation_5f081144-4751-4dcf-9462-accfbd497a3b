<template>
	<view class="user-info-update">
		<view class="container">
			<view class="header">
				<text class="title">人员信息更新</text>
				<text class="subtitle">完善您的个人信息</text>
			</view>

			<!-- 加载中 -->
			<view v-if="loading" class="loading-section">
				<u-loading mode="circle"></u-loading>
				<text class="loading-text">正在加载用户信息...</text>
			</view>

			<!-- 未找到人员信息 -->
			<view v-if="!loading && !userFound" class="not-found-section">
				<view class="not-found-icon">❌</view>
				<view class="not-found-title">未找到人员信息</view>
				<view class="not-found-text">当前用户ID：{{ currentUserId }}</view>
				<view class="not-found-text">请联系管理员添加您的基础信息</view>
				<u-button type="primary" @click="loadUserInfo" class="retry-btn">重新加载</u-button>
			</view>

			<!-- 人员信息编辑表单 -->
			<view v-if="!loading && userFound" class="edit-section">
				<view class="user-basic-info">
					<view class="user-name">{{ formData.realName || '未知' }} 的信息</view>
					<view class="id-number">身份证：{{ formData.identificationNumber }}</view>
				</view>

				<u-form :model="formData" ref="formRef" :rules="formRules" label-width="120" :errorType="['toast']">
					<!-- 基本信息 -->
					<u-form-item label="姓名" prop="realName" required>
						<u-input v-model="formData.realName" placeholder="请输入姓名" />
					</u-form-item>

					<u-form-item label="年龄" prop="age" required>
						<u-input v-model="formData.age" type="number" placeholder="请输入年龄" />
					</u-form-item>

					<u-form-item label="性别" prop="gender" required>
						<jnpf-select v-model="formData.gender" placeholder="请选择性别" :options="genderOptions" :props="props" />
					</u-form-item>

					<u-form-item label="联系方式" prop="mobilePhone" required>
						<u-input v-model="formData.mobilePhone" placeholder="请输入手机号码" />
					</u-form-item>

					<u-form-item label="紧急联系人" prop="emergencyContacts" required>
						<u-input v-model="formData.emergencyContacts" placeholder="请输入紧急联系人" />
					</u-form-item>

					<u-form-item label="紧急联系人电话" prop="emergencyContactsPhone" required>
						<u-input v-model="formData.emergencyContactsPhone" placeholder="请输入紧急联系人电话" />
					</u-form-item>

					<u-form-item label="籍贯" prop="nativePlace" required>
						<u-input v-model="formData.nativePlace" placeholder="请输入籍贯" />
					</u-form-item>

					<u-form-item label="民族" prop="nation" required>
						<jnpf-select v-model="formData.nation" placeholder="请选择民族" :options="nationOptions" />
					</u-form-item>

					<u-form-item label="家庭住址" prop="homeAddress" required>
						<u-input v-model="formData.homeAddress" placeholder="请输入地址（精确到村/小区门牌号）" />
					</u-form-item>

					<u-form-item label="政治面貌" prop="politicalOutlook" required>
						<jnpf-select v-model="formData.politicalOutlook" placeholder="请选择政治面貌" :options="politicalOutlookOptions" />
					</u-form-item>

					<u-form-item label="学历" prop="education" required>
						<jnpf-select v-model="formData.education" placeholder="请选择学历" :options="educationOptions" />
					</u-form-item>

					<u-form-item label="所学专业" prop="specialty" required>
						<u-input v-model="formData.specialty" placeholder="小学、初中、高中学历人员填'无'" />
					</u-form-item>

					<u-form-item label="毕业学校" prop="graduationSchool" required>
						<u-input v-model="formData.graduationSchool" placeholder="请输入毕业学校" />
					</u-form-item>

					<u-form-item label="毕业时间" prop="graduationTime" required>
						<jnpf-date-time type="date" v-model="formData.graduationTime" placeholder="请选择毕业时间" format="YYYY-MM-DD" />
					</u-form-item>

					<u-form-item label="首次参加工作时间" prop="joinWorkTime" required>
						<jnpf-date-time type="date" v-model="formData.joinWorkTime" placeholder="请选择首次参加工作时间" format="YYYY-MM-DD" />
					</u-form-item>

					<u-form-item label="本工种工作年限" prop="seniority" required>
						<u-input v-model="formData.seniority" type="number" placeholder="如之前未进入核电工作，则填写0" />
					</u-form-item>

					<u-form-item label="婚姻状况" prop="maritalStatus" required>
						<jnpf-select v-model="formData.maritalStatus" placeholder="请选择婚姻状况" :options="maritalStatusOptions" />
					</u-form-item>

					<!-- 只读字段 -->
					<u-form-item label="部门">
						<u-input v-model="formData.organizeName" placeholder="部门" readonly />
					</u-form-item>

					<u-form-item label="岗位">
						<u-input v-model="formData.positionName" placeholder="岗位" readonly />
					</u-form-item>

					<u-form-item label="岗位序列" prop="positionSequence">
						<jnpf-select v-model="formData.positionSequence" placeholder="请选择岗位序列" :options="positionSequenceOptions" />
					</u-form-item>

					<u-form-item label="人员类别" prop="categoryPersonnel" required>
						<u-input v-model="formData.categoryPersonnel" placeholder="请输入人员类别" />
					</u-form-item>

					<u-form-item label="用工方式">
						<u-input v-model="formData.formEmployment" placeholder="用工方式" readonly />
					</u-form-item>

					<u-form-item label="来源单位">
						<u-input v-model="formData.sourceUnit" placeholder="来源单位" readonly />
					</u-form-item>

					<u-form-item label="进项目时间">
						<jnpf-date-time type="date" v-model="formData.goProjectTime" placeholder="进项目时间" format="YYYY-MM-DD" readonly />
					</u-form-item>

					<u-form-item label="备注">
						<u-input v-model="formData.remark" type="textarea" placeholder="请输入备注" />
					</u-form-item>
				</u-form>

				<view class="form-actions">
					<u-button type="primary" @click="handleSubmit" :loading="submitting" class="submit-btn">保存更新</u-button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import removeUserApi from '@/api/basic/removeUser.js'
import { getDictionaryDataSelector } from '@/api/common'
import { mapState } from 'vuex'

export default {
	name: "UserInfoUpdate",
	data() {
		return {
			loading: true,
			userFound: false,
			submitting: false,
			currentUserId: '',
			
			// 编辑表单数据
			formData: {
				id: '',
				realName: '',
				age: null,
				gender: 1,
				mobilePhone: '',
				emergencyContacts: '',
				emergencyContactsPhone: '',
				nativePlace: '',
				nation: '',
				homeAddress: '',
				politicalOutlook: '',
				education: '',
				specialty: '',
				graduationSchool: '',
				graduationTime: null,
				joinWorkTime: null,
				seniority: null,
				maritalStatus: '',
				organizeName: '',
				positionName: '',
				positionSequence: '',
				categoryPersonnel: '',
				formEmployment: '',
				sourceUnit: '',
				goProjectTime: null,
				remark: ''
			},
			
			// 下拉选项数据
			educationOptions: [],
			nationOptions: [],
			positionSequenceOptions: [],

			props: {
				label: 'fullName',
				value: 'id'
			},

			// 表单验证规则
			formRules: {
				realName: [
					{ required: true, message: '请输入姓名', trigger: 'blur' }
				],
				age: [
					{ required: true, message: '请输入年龄', trigger: 'change' }
				],
				gender: [
					{ required: true, message: '请选择性别', trigger: 'change' }
				],
				mobilePhone: [
					{ required: true, message: '请输入手机号', trigger: 'blur' },
					{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
				],
				homeAddress: [
					{ required: true, message: '请输入地址', trigger: 'blur' }
				],
				politicalOutlook: [
					{ required: true, message: '请选择政治面貌', trigger: 'change' }
				],
				graduationSchool: [
					{ required: true, message: '请输入毕业学校', trigger: 'blur' }
				],
				graduationTime: [
					{ required: true, message: '请选择毕业时间', trigger: 'change' }
				],
				joinWorkTime: [
					{ required: true, message: '请选择首次参加工作时间', trigger: 'change' }
				],
				seniority: [
					{ required: true, message: '请输入从事本工种工作年限', trigger: 'change' }
				],
				maritalStatus: [
					{ required: true, message: '请选择婚姻状况', trigger: 'change' }
				],
				emergencyContacts: [
					{ required: true, message: '请输入紧急联系人', trigger: 'blur' }
				],
				emergencyContactsPhone: [
					{ required: true, message: '请输入紧急联系人电话', trigger: 'blur' },
					{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
				],
				nativePlace: [
					{ required: true, message: '请输入籍贯', trigger: 'blur' }
				],
				nation: [
					{ required: true, message: '请选择民族', trigger: 'change' }
				],
				education: [
					{ required: true, message: '请选择学历', trigger: 'change' }
				],
				specialty: [
					{ required: true, message: '请输入所学专业', trigger: 'blur' }
				],
				categoryPersonnel: [
					{ required: true, message: '请输入人员类别', trigger: 'blur' }
				]
			}
		}
	},
	computed: {
		...mapState('user', ['userInfo'])
	},
	onLoad() {
		const userInfo = uni.getStorageSync('userInfo') || {}
		this.currentUserId = userInfo.userId || ''
		this.getDictionaryData();
		this.loadUserInfo()
	},
	methods: {
		// 获取字典数据
		async getDictionaryData() {
			try {
				const [eduRes, nationRes, posRes, genderRes, politicalRes, maritalRes] = await Promise.all([
					getDictionaryDataSelector("Education"),      // 学历
					getDictionaryDataSelector("Nation"),          // 民族
					getDictionaryDataSelector("PositionSequence"),// 岗位序列
				]);

				const processOptions = (res) => {
					if (res && res.data.list && Array.isArray(res.data.list)) {
						return res.data.list.map(item => ({ ...item, id: item.enCode || item.id }));
					} else {
						return [];
					}
				};

				this.educationOptions = processOptions(eduRes);
				this.nationOptions = processOptions(nationRes);
				this.positionSequenceOptions = processOptions(posRes);

			} catch (error) {
				console.error('获取数据字典失败', error);
				uni.showToast({ title: '字典数据加载失败', icon: 'none' });
			}
		},

		// 加载用户信息
		async loadUserInfo() {
			if (!this.currentUserId) {
				uni.showToast({ title: '未获取到用户ID', icon: 'none' });
				this.loading = false
				this.userFound = false
				return
			}

			this.loading = true
			const params = {
				id: this.currentUserId,
				currentPage: 1,
				pageSize: 10
			}
			removeUserApi.pageOptimized(params).then(res => {
				if (res.data && res.data.list && res.data.list.length > 0) {
					const user = res.data.list[0];
					// 填充表单数据
					Object.keys(this.formData).forEach(key => {
						if (user[key] !== undefined && user[key] !== null) {
							this.formData[key] = user[key];
						}
					});
					this.userFound = true;
				} else {
					this.userFound = false;
					uni.showToast({ title: '未查询到用户信息', icon: 'none' });
				}
				this.loading = false;
			}).catch(err => {
				this.loading = false;
				this.userFound = false;
				uni.showToast({ title: '加载用户信息失败', icon: 'none' });
			})
		},

		// 提交表单
		handleSubmit() {
			this.$refs.formRef.validate(valid => {
				if (valid) {
					this.submitting = true;
					userInfoRegistryApi.update(this.formData.id, this.formData).then(res => {
						uni.showToast({ title: res.message, icon: 'success' });
						this.submitting = false;
					}).catch(() => {
						this.submitting = false;
					});
				}
			});
		}
	}
}
</script>

<style scoped lang="scss">
.user-info-update {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.container {
	padding: 20rpx;
}

.header {
	background: white;
	padding: 30rpx;
	margin-bottom: 20rpx;
	border-radius: 10rpx;
	text-align: center;

	.title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
		display: block;
		margin-bottom: 10rpx;
	}

	.subtitle {
		font-size: 28rpx;
		color: #666;
		display: block;
	}
}

.loading-section {
	background: white;
	padding: 60rpx 30rpx;
	border-radius: 10rpx;
	text-align: center;
	margin-bottom: 20rpx;

	.loading-text {
		font-size: 28rpx;
		color: #666;
		margin-top: 20rpx;
		display: block;
	}
}

.not-found-section {
	background: white;
	padding: 60rpx 30rpx;
	border-radius: 10rpx;
	text-align: center;

	.not-found-icon {
		font-size: 96rpx;
		margin-bottom: 30rpx;
	}

	.not-found-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
	}

	.not-found-text {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 16rpx;
	}

	.retry-btn {
		margin-top: 30rpx;
		width: 200rpx;
	}
}

.edit-section {
	background: white;
	border-radius: 10rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.user-basic-info {
	background: #f5f5f5;
	padding: 30rpx;
	border-radius: 10rpx;
	margin-bottom: 30rpx;
	text-align: center;

	.user-name {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 10rpx;
	}

	.id-number {
		font-size: 28rpx;
		color: #666;
	}
}

.form-actions {
	margin-top: 40rpx;
	padding-top: 30rpx;
	border-top: 2rpx solid #f0f0f0;

	.submit-btn {
		width: 100%;
	}
}

// 覆盖uview组件样式
:deep(.u-form-item) {
	margin-bottom: 30rpx;
}

:deep(.u-form-item__label) {
	font-size: 28rpx;
	color: #333;
}

:deep(.u-input__content__field-wrapper__field) {
	font-size: 28rpx;
}

:deep(.u-radio-group) {
	flex-direction: row;
}

:deep(.u-button) {
	height: 88rpx;
	font-size: 32rpx;
}

// 响应式设计
@media (max-width: 768px) {
	.form-actions {
		.submit-btn {
			width: 100%;
		}
	}
}
</style>
